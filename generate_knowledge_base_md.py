#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成知识库MD文件脚本
查询指定父级知识库下的所有子知识库，并为每个知识库生成包含.md文件列表的MD文档
"""

from pymongo import MongoClient
from bson import ObjectId
import os

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # 根据实际情况修改连接字符串
        client = MongoClient('mongodb://localhost:27017/')
        db = client['fastgpt']  # 根据实际数据库名称修改
        return db
    except Exception as e:
        print(f"连接MongoDB失败: {e}")
        return None

def get_child_datasets(db, parent_id):
    """获取指定父级ID下的所有子知识库"""
    try:
        datasets = list(db['datasets'].find({"parentId": ObjectId(parent_id)}))
        return datasets
    except Exception as e:
        print(f"查询子知识库失败: {e}")
        return []

def get_md_files_for_dataset(db, dataset_id):
    """获取指定知识库下的所有.md文件"""
    try:
        # 查询指定知识库下以.md结尾的文档集合
        md_files = list(db['dataset_collections'].find({
            "datasetId": dataset_id,
            "name": {"$regex": r"\.md$", "$options": "i"}  # 不区分大小写匹配.md结尾
        }).sort("name", 1))
        return md_files
    except Exception as e:
        print(f"查询MD文件失败: {e}")
        return []

def generate_md_file(dataset_name, dataset_intro, md_files):
    """生成知识库的MD文件内容"""
    content = f"# {dataset_name}\n\n"
    
    if dataset_intro:
        content += f"**知识库简介**: {dataset_intro}\n\n"
    
    content += "## MD文档列表\n\n"
    
    if md_files:
        for file_info in md_files:
            file_name = file_info.get('name', '')
            content += f"{file_name} - {dataset_name}\n"
    else:
        content += "该知识库暂无.md文档\n"
    
    return content

def save_md_file(filename, content):
    """保存MD文件到本地"""
    try:
        # 确保文件名安全
        safe_filename = "".join(c for c in filename if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
        if not safe_filename.endswith('.md'):
            safe_filename += '.md'
        
        with open(safe_filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已生成文件: {safe_filename}")
        return True
    except Exception as e:
        print(f"保存文件失败 {filename}: {e}")
        return False

def main():
    """主函数"""
    # 指定的父级知识库ID
    parent_id = "675f86de7bb3c4679814c738"
    
    # 连接数据库
    db = connect_to_mongodb()
    if not db:
        return
    
    # 获取子知识库列表
    print(f"正在查询父级知识库 {parent_id} 下的子知识库...")
    child_datasets = get_child_datasets(db, parent_id)
    
    if not child_datasets:
        print("未找到子知识库")
        return
    
    print(f"找到 {len(child_datasets)} 个子知识库")
    
    # 为每个子知识库生成MD文件
    for dataset in child_datasets:
        dataset_id = dataset['_id']
        dataset_name = dataset.get('name', '未命名知识库')
        dataset_intro = dataset.get('intro', '')
        
        print(f"\n处理知识库: {dataset_name}")
        
        # 获取该知识库下的.md文件
        md_files = get_md_files_for_dataset(db, dataset_id)
        print(f"找到 {len(md_files)} 个.md文件")
        
        # 生成MD文件内容
        content = generate_md_file(dataset_name, dataset_intro, md_files)
        
        # 保存文件
        save_md_file(dataset_name, content)
    
    print(f"\n处理完成！共处理了 {len(child_datasets)} 个知识库")

if __name__ == "__main__":
    main()
