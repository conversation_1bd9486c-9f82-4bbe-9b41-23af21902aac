# FastGPT数据库结构分析文档

## 概述

本文档详细分析了FastGPT知识库系统中的MongoDB数据库结构，包括各个集合的字段定义、字段含义以及集合之间的关系。该分析基于项目接口文件`zhishiku_h5.py`和数据库样本数据。

## 数据库集合概览

FastGPT系统主要包含以下8个核心集合：

1. **datasets** - 数据集（知识库）
2. **dataset_collections** - 数据集合（文档集合）
3. **dataset_datas** - 数据集数据（知识片段）
4. **chats** - 聊天会话
5. **chatitems** - 聊天消息项
6. **images** - 图片资源
7. **dataset_trainings** - 数据集训练任务
8. **files_list_wh** - 文件清单

## 详细集合分析

### 1. datasets（数据集/知识库）

**用途**：存储知识库的基本信息

**主要字段**：
- `_id`: ObjectId - 数据集唯一标识符
- `teamId`: ObjectId - 团队ID，关联团队信息
- `tmbId`: ObjectId - 团队成员ID
- `parentId`: ObjectId - 父级数据集ID（用于层级结构）
- `type`: String - 数据集类型（如"dataset"）
- `avatar`: String - 数据集头像/图标
- `name`: String - 数据集名称
- `intro`: String - 数据集介绍
- `status`: String - 状态（如"active"）
- `permission`: String - 权限设置
- `createTime`: Date - 创建时间
- `updateTime`: Date - 更新时间
- `vectorModel`: Object - 向量模型配置
- `agentModel`: Object - 智能体模型配置
- `websiteConfig`: Object - 网站配置
- `externalReadUrl`: String - 外部读取URL

**关系**：
- 一对多关系：一个dataset可以包含多个dataset_collections
- 层级关系：通过parentId实现数据集的层级结构

### 2. dataset_collections（数据集合/文档集合）

**用途**：存储文档集合信息，每个集合代表一个上传的文件或文档

**主要字段**：
- `_id`: ObjectId - 集合唯一标识符
- `teamId`: ObjectId - 团队ID
- `tmbId`: ObjectId - 团队成员ID
- `datasetId`: ObjectId - 所属数据集ID（外键关联datasets）
- `parentId`: ObjectId - 父级集合ID
- `name`: String - 集合名称（通常是文件名）
- `type`: String - 集合类型（如"file", "folder"）
- `createTime`: Date - 创建时间
- `updateTime`: Date - 更新时间
- `trainingType`: String - 训练类型（如"chunk", "qa"）
- `chunkSize`: Number - 分块大小
- `chunkSplitter`: String - 分块分隔符
- `qaPrompt`: String - QA提示词
- `forbid`: Boolean - 是否禁用
- `metadata`: Object - 元数据
  - `source`: String - 来源（如"api", "bs"）
  - `audit`: String - 审核状态（"0"待审核, "1"已通过）
  - `cpxh`: String - 产品型号
  - `cpmc`: String - 产品名称
  - `size`: Number - 文件大小
  - `isindex`: String - 是否已索引（"0"未索引, "1"已索引）

**关系**：
- 多对一关系：多个collections属于一个dataset
- 一对多关系：一个collection包含多个dataset_datas

### 3. dataset_datas（数据集数据/知识片段）

**用途**：存储具体的知识片段数据

**主要字段**：
- `_id`: ObjectId - 数据唯一标识符
- `teamId`: ObjectId - 团队ID
- `tmbId`: ObjectId - 团队成员ID
- `datasetId`: ObjectId - 所属数据集ID
- `collectionId`: ObjectId - 所属集合ID（外键关联dataset_collections）
- `q`: String - 问题/内容
- `a`: String - 答案/回答
- `chunkIndex`: Number - 分块索引
- `indexes`: Array - 索引数组
  - `defaultIndex`: Boolean - 是否为默认索引
  - `dataId`: String - 数据ID
  - `text`: String - 索引文本
  - `_id`: ObjectId - 索引ID
- `sourceId`: ObjectId - 源文件ID
- `sourceName`: String - 源文件名
- `score`: Array - 评分数组
- `tokens`: Number - token数量
- `updateTime`: Date - 更新时间

**关系**：
- 多对一关系：多个datas属于一个collection
- 包含关系：每个data包含多个indexes用于搜索

### 4. chats（聊天会话）

**用途**：存储聊天会话的基本信息

**主要字段**：
- `_id`: ObjectId - 会话唯一标识符
- `teamId`: ObjectId - 团队ID
- `tmbId`: ObjectId - 团队成员ID
- `appId`: ObjectId - 应用ID
- `title`: String - 会话标题
- `customTitle`: String - 自定义标题
- `top`: Boolean - 是否置顶
- `createTime`: Date - 创建时间
- `updateTime`: Date - 更新时间
- `source`: String - 来源
- `shareId`: String - 分享ID
- `outLinkUid`: String - 外链用户ID
- `metadata`: Object - 元数据

**关系**：
- 一对多关系：一个chat包含多个chatitems

### 5. chatitems（聊天消息项）

**用途**：存储具体的聊天消息内容

**主要字段**：
- `_id`: ObjectId - 消息唯一标识符
- `teamId`: ObjectId - 团队ID
- `tmbId`: ObjectId - 团队成员ID
- `chatId`: String - 聊天会话ID（外键关联chats）
- `dataId`: String - 数据ID
- `appId`: ObjectId - 应用ID
- `hideInUI`: Boolean - 是否在UI中隐藏
- `obj`: String - 对象类型（如"AI", "Human"）
- `value`: Array - 消息值数组
  - `type`: String - 类型（如"text"）
  - `text`: Object - 文本内容
    - `content`: String - 内容
- `customFeedbacks`: Array - 自定义反馈
- `responseData`: Array - 响应数据
- `time`: Date - 时间
- `isread`: Number - 是否已读

**关系**：
- 多对一关系：多个chatitems属于一个chat

### 6. images（图片资源）

**用途**：存储图片资源数据

**主要字段**：
- `_id`: ObjectId - 图片唯一标识符
- `teamId`: ObjectId - 团队ID
- `binary`: String - 图片二进制数据（Base64编码）
- `type`: String - 类型（如"collectionImage"）
- `metadata`: Object - 元数据
  - `mime`: String - MIME类型
  - `relatedId`: String - 关联ID
- `createTime`: Date - 创建时间

**关系**：
- 通过metadata.relatedId关联到其他集合的文档

### 7. dataset_trainings（数据集训练任务）

**用途**：存储数据集训练任务信息

**主要字段**：
- `_id`: ObjectId - 训练任务唯一标识符
- `teamId`: ObjectId - 团队ID
- `tmbId`: ObjectId - 团队成员ID
- `datasetId`: ObjectId - 数据集ID
- `collectionId`: ObjectId - 集合ID
- `billId`: ObjectId - 账单ID
- `mode`: String - 模式（如"chunk", "parse"）
- `retryCount`: Number - 重试次数
- `q`: String - 问题
- `a`: String - 答案
- `chunkIndex`: Number - 分块索引
- `indexSize`: Number - 索引大小
- `weight`: Number - 权重
- `indexes`: Array - 索引数组
- `expireAt`: Date - 过期时间
- `lockTime`: Date - 锁定时间
- `errorMsg`: String - 错误消息

**关系**：
- 关联dataset_collections进行训练任务管理

### 8. files_list_wh（文件清单）

**用途**：存储已审核通过的文件清单

**主要字段**：
- `_id`: ObjectId - 清单唯一标识符
- `datasetId`: String - 数据集ID
- `datasetname`: String - 数据集名称
- `collectionId`: String - 集合ID
- `name`: String - 文件名称
- `creatTime`: Date - 创建时间
- `metadata`: Object - 元数据

**关系**：
- 记录已审核通过的dataset_collections

## 集合关系图

```
datasets (知识库)
    ↓ (1:N)
dataset_collections (文档集合)
    ↓ (1:N)
dataset_datas (知识片段)
    ↓ (关联)
images (图片资源)

chats (聊天会话)
    ↓ (1:N)
chatitems (聊天消息)

dataset_trainings (训练任务) → dataset_collections
files_list_wh (文件清单) → dataset_collections
```

## 核心业务流程

### 1. 知识库文档上传流程
1. 创建dataset_collections记录
2. 文档解析生成多个dataset_datas
3. 创建dataset_trainings训练任务
4. 审核通过后写入files_list_wh

### 2. 聊天对话流程
1. 创建chats会话
2. 每次对话创建chatitems记录
3. AI响应时关联相关的dataset_datas

### 3. 索引生成流程
1. 通过dataset_trainings处理文档
2. 生成indexes索引数据
3. 更新dataset_datas的indexes字段

## 注意事项

1. **数据一致性**：各集合通过ObjectId建立关联关系
2. **权限控制**：通过teamId和tmbId实现多租户隔离
3. **审核机制**：dataset_collections通过metadata.audit字段控制审核状态
4. **索引优化**：dataset_datas的indexes字段用于向量搜索
5. **训练状态**：dataset_trainings记录训练任务的执行状态

此文档为后续开发提供了完整的数据库结构参考，便于理解系统架构和数据流转。
